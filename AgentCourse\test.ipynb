{"cells": [{"cell_type": "markdown", "id": "d0cc4adf", "metadata": {}, "source": ["### Question data"]}, {"cell_type": "code", "execution_count": 2, "id": "14e3f417", "metadata": {}, "outputs": [], "source": ["# Load metadata.jsonl\n", "import json\n", "# Load the metadata.jsonl file\n", "with open('metadata.jsonl', 'r') as jsonl_file:\n", "    json_list = list(jsonl_file)\n", "\n", "json_QA = []\n", "for json_str in json_list:\n", "    json_data = json.loads(json_str)\n", "    json_QA.append(json_data)"]}, {"cell_type": "code", "execution_count": 3, "id": "5e2da6fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "Task ID: ed58682d-bc52-4baa-9eb0-4eb81e1edacc\n", "Question: What is the last word before the second chorus of the King of Pop's fifth single from his sixth studio album?\n", "Level: 2\n", "Final Answer: stare\n", "Annotator Metadata: \n", "  ├── Steps: \n", "  │      ├── 1. Google searched \"King of Pop\".\n", "  │      ├── 2. Clicked on <PERSON>'s Wikipedia.\n", "  │      ├── 3. Scrolled down to \"Discography\".\n", "  │      ├── 4. Clicked on the sixth album, \"Thriller\".\n", "  │      ├── 5. Looked under \"Singles from Thriller\".\n", "  │      ├── 6. <PERSON>licked on the fifth single, \"Human Nature\".\n", "  │      ├── 7. Google searched \"Human Nature Michael <PERSON> Lyrics\".\n", "  │      ├── 8. Looked at the opening result with full lyrics sourced by Musixmatch.\n", "  │      ├── 9. Looked for repeating lyrics to determine the chorus.\n", "  │      ├── 10. Determined the chorus begins with \"If they say\" and ends with \"Does he do me that way?\"\n", "  │      ├── 11. Found the second instance of the chorus within the lyrics.\n", "  │      ├── 12. Noted the last word before the second chorus - \"stare\".\n", "  ├── Number of steps: 12\n", "  ├── How long did this take?: 20 minutes\n", "  ├── Tools:\n", "  │      ├── Web Browser\n", "  └── Number of tools: 1\n", "==================================================\n"]}], "source": ["# randomly select 3 samples\n", "# {\"task_id\": \"c61d22de-5f6c-4958-a7f6-5e9707bd3466\", \"Question\": \"A paper about AI regulation that was originally submitted to arXiv.org in June 2022 shows a figure with three axes, where each axis has a label word at both ends. Which of these words is used to describe a type of society in a Physics and Society article submitted to arXiv.org on August 11, 2016?\", \"Level\": 2, \"Final answer\": \"egalitarian\", \"file_name\": \"\", \"Annotator Metadata\": {\"Steps\": \"1. Go to arxiv.org and navigate to the Advanced Search page.\\n2. Enter \\\"AI regulation\\\" in the search box and select \\\"All fields\\\" from the dropdown.\\n3. Enter 2022-06-01 and 2022-07-01 into the date inputs, select \\\"Submission date (original)\\\", and submit the search.\\n4. Go through the search results to find the article that has a figure with three axes and labels on each end of the axes, titled \\\"Fairness in Agreement With European Values: An Interdisciplinary Perspective on AI Regulation\\\".\\n5. Note the six words used as labels: deontological, egalitarian, localized, standardized, utilitarian, and consequential.\\n6. Go back to arxiv.org\\n7. Find \\\"Physics and Society\\\" and go to the page for the \\\"Physics and Society\\\" category.\\n8. Note that the tag for this category is \\\"physics.soc-ph\\\".\\n9. Go to the Advanced Search page.\\n10. Enter \\\"physics.soc-ph\\\" in the search box and select \\\"All fields\\\" from the dropdown.\\n11. Enter 2016-08-11 and 2016-08-12 into the date inputs, select \\\"Submission date (original)\\\", and submit the search.\\n12. Search for instances of the six words in the results to find the paper titled \\\"Phase transition from egalitarian to hierarchical societies driven by competition between cognitive and social constraints\\\", indicating that \\\"egalitarian\\\" is the correct answer.\", \"Number of steps\": \"12\", \"How long did this take?\": \"8 minutes\", \"Tools\": \"1. Web browser\\n2. Image recognition tools (to identify and parse a figure with three axes)\", \"Number of tools\": \"2\"}}\n", "\n", "import random\n", "# random.seed(42)\n", "random_samples = random.sample(json_QA, 1)\n", "for sample in random_samples:\n", "    print(\"=\" * 50)\n", "    print(f\"Task ID: {sample['task_id']}\")\n", "    print(f\"Question: {sample['Question']}\")\n", "    print(f\"Level: {sample['Level']}\")\n", "    print(f\"Final Answer: {sample['Final answer']}\")\n", "    print(f\"Annotator Metadata: \")\n", "    print(f\"  ├── Steps: \")\n", "    for step in sample['Annotator Metadata']['Steps'].split('\\n'):\n", "        print(f\"  │      ├── {step}\")\n", "    print(f\"  ├── Number of steps: {sample['Annotator Metadata']['Number of steps']}\")\n", "    print(f\"  ├── How long did this take?: {sample['Annotator Metadata']['How long did this take?']}\")\n", "    print(f\"  ├── Tools:\")\n", "    for tool in sample['Annotator Metadata']['Tools'].split('\\n'):\n", "        print(f\"  │      ├── {tool}\")\n", "    print(f\"  └── Number of tools: {sample['Annotator Metadata']['Number of tools']}\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": 56, "id": "4bb02420", "metadata": {}, "outputs": [], "source": ["### build a vector database based on the metadata.jsonl\n", "# https://python.langchain.com/docs/integrations/vectorstores/supabase/\n", "import os\n", "from dotenv import load_dotenv\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "from langchain_community.vectorstores import SupabaseVectorStore\n", "from supabase.client import Client, create_client\n", "\n", "\n", "load_dotenv()\n", "embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-mpnet-base-v2\") #  dim=768\n", "\n", "supabase_url = os.environ.get(\"SUPABASE_URL\")\n", "supabase_key = os.environ.get(\"SUPABASE_SERVICE_KEY\")\n", "supabase: Client = create_client(supabase_url, supabase_key)"]}, {"cell_type": "code", "execution_count": null, "id": "a070b955", "metadata": {}, "outputs": [], "source": ["# wrap the metadata.jsonl's questions and answers into a list of document\n", "from langchain.schema import Document\n", "docs = []\n", "for sample in json_QA:\n", "    content = f\"Question : {sample['Question']}\\n\\nFinal answer : {sample['Final answer']}\"\n", "    doc = {\n", "        \"content\" : content,\n", "        \"metadata\" : { # meatadata的格式必须时source键，否则会报错\n", "            \"source\" : sample['task_id']\n", "        },\n", "        \"embedding\" : embeddings.embed_query(content),\n", "    }\n", "    docs.append(doc)\n", "\n", "# upload the documents to the vector database\n", "try:\n", "    response = (\n", "        supabase.table(\"documents\")\n", "        .insert(docs)\n", "        .execute()\n", "    )\n", "except Exception as exception:\n", "    print(\"Error inserting data into Supabase:\", exception)\n", "\n", "# ALTERNATIVE : Save the documents (a list of dict) into a csv file, and manually upload it to Supabase\n", "# import pandas as pd\n", "# df = pd.DataFrame(docs)\n", "# df.to_csv('supabase_docs.csv', index=False)"]}, {"cell_type": "code", "execution_count": 54, "id": "77fb9dbb", "metadata": {}, "outputs": [], "source": ["# add items to vector database\n", "vector_store = SupabaseVectorStore(\n", "    client=supabase,\n", "    embedding= embeddings,\n", "    table_name=\"documents\",\n", "    query_name=\"match_documents_langchain\",\n", ")\n", "retriever = vector_store.as_retriever()"]}, {"cell_type": "code", "execution_count": 55, "id": "12a05971", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"data": {"text/plain": ["Document(metadata={'source': '840bfca7-4f7b-481a-8794-c560c340185d'}, page_content='Question : On June 6, 2023, an article by <PERSON> was published in Universe Today. This article mentions a team that produced a paper about their observations, linked at the bottom of the article. Find this paper. Under what NASA award number was the work performed by <PERSON><PERSON> <PERSON><PERSON> supported by?\\n\\nFinal answer : 80GSFC21M0002')"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"On June 6, 2023, an article by <PERSON> was published in Universe Today. This article mentions a team that produced a paper about their observations, linked at the bottom of the article. Find this paper. Under what NASA award number was the work performed by <PERSON><PERSON> <PERSON><PERSON> supported by?\"\n", "# matched_docs = vector_store.similarity_search(query, 2)\n", "docs = retriever.invoke(query)\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 31, "id": "1eae5ba4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["List of tools used in all samples:\n", "Total number of tools used: 83\n", "  ├── web browser: 107\n", "  ├── image recognition tools (to identify and parse a figure with three axes): 1\n", "  ├── search engine: 101\n", "  ├── calculator: 34\n", "  ├── unlambda compiler (optional): 1\n", "  ├── a web browser.: 2\n", "  ├── a search engine.: 2\n", "  ├── a calculator.: 1\n", "  ├── microsoft excel: 5\n", "  ├── google search: 1\n", "  ├── ne: 9\n", "  ├── pdf access: 7\n", "  ├── file handling: 2\n", "  ├── python: 3\n", "  ├── image recognition tools: 12\n", "  ├── jsonld file access: 1\n", "  ├── video parsing: 1\n", "  ├── python compiler: 1\n", "  ├── video recognition tools: 3\n", "  ├── pdf viewer: 7\n", "  ├── microsoft excel / google sheets: 3\n", "  ├── word document access: 1\n", "  ├── tool to extract text from images: 1\n", "  ├── a word reversal tool / script: 1\n", "  ├── counter: 1\n", "  ├── excel: 3\n", "  ├── image recognition: 5\n", "  ├── color recognition: 3\n", "  ├── excel file access: 3\n", "  ├── xml file access: 1\n", "  ├── access to the internet archive, web.archive.org: 1\n", "  ├── text processing/diff tool: 1\n", "  ├── gif parsing tools: 1\n", "  ├── a web browser: 7\n", "  ├── a search engine: 7\n", "  ├── a speech-to-text tool: 2\n", "  ├── code/data analysis tools: 1\n", "  ├── audio capability: 2\n", "  ├── pdf reader: 1\n", "  ├── markdown: 1\n", "  ├── a calculator: 5\n", "  ├── access to wikipedia: 3\n", "  ├── image recognition/ocr: 3\n", "  ├── google translate access: 1\n", "  ├── ocr: 4\n", "  ├── bass note data: 1\n", "  ├── text editor: 1\n", "  ├── xlsx file access: 1\n", "  ├── powerpoint viewer: 1\n", "  ├── csv file access: 1\n", "  ├── calculator (or use excel): 1\n", "  ├── computer algebra system: 1\n", "  ├── video processing software: 1\n", "  ├── audio processing software: 1\n", "  ├── computer vision: 1\n", "  ├── google maps: 1\n", "  ├── access to excel files: 1\n", "  ├── calculator (or ability to count): 1\n", "  ├── a file interface: 3\n", "  ├── a python ide: 1\n", "  ├── spreadsheet editor: 1\n", "  ├── tools required: 1\n", "  ├── b browser: 1\n", "  ├── image recognition and processing tools: 1\n", "  ├── computer vision or ocr: 1\n", "  ├── c++ compiler: 1\n", "  ├── access to google maps: 1\n", "  ├── youtube player: 1\n", "  ├── natural language processor: 1\n", "  ├── graph interaction tools: 1\n", "  ├── bablyonian cuniform -> arabic legend: 1\n", "  ├── access to youtube: 1\n", "  ├── image search tools: 1\n", "  ├── calculator or counting function: 1\n", "  ├── a speech-to-text audio processing tool: 1\n", "  ├── access to academic journal websites: 1\n", "  ├── pdf reader/extracter: 1\n", "  ├── rubik's cube model: 1\n", "  ├── wikipedia: 1\n", "  ├── video capability: 1\n", "  ├── image processing tools: 1\n", "  ├── age recognition software: 1\n", "  ├── youtube: 1\n"]}], "source": ["# list of the tools used in all the samples\n", "from collections import Counter, OrderedDict\n", "\n", "tools = []\n", "for sample in json_QA:\n", "    for tool in sample['Annotator Metadata']['Tools'].split('\\n'):\n", "        tool = tool[2:].strip().lower()\n", "        if tool.startswith(\"(\"):\n", "            tool = tool[11:].strip()\n", "        tools.append(tool)\n", "tools_counter = OrderedDict(Counter(tools))\n", "print(\"List of tools used in all samples:\")\n", "print(\"Total number of tools used:\", len(tools_counter))\n", "for tool, count in tools_counter.items():\n", "    print(f\"  ├── {tool}: {count}\")"]}, {"cell_type": "markdown", "id": "5efee12a", "metadata": {}, "source": ["#### Graph"]}, {"cell_type": "code", "execution_count": 55, "id": "7fe573cc", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\n", "You are a helpful assistant tasked with answering questions using a set of tools.\n", "If the tool is not available, you can try to find the information online. You can also use your own knowledge to answer the question. \n", "You need to provide a step-by-step explanation of how you arrived at the answer.\n", "==========================\n", "Here is a few examples showing you how to answer the question step by step.\n", "\"\"\"\n", "for i, samples in enumerate(random_samples):\n", "    system_prompt += f\"\\nQuestion {i+1}: {samples['Question']}\\nSteps:\\n{samples['Annotator Metadata']['Steps']}\\nTools:\\n{samples['Annotator Metadata']['Tools']}\\nFinal Answer: {samples['Final answer']}\\n\"\n", "system_prompt += \"\\n==========================\\n\"\n", "system_prompt += \"Now, please answer the following question step by step.\\n\"\n", "\n", "# save the system_prompt to a file\n", "with open('system_prompt.txt', 'w') as f:\n", "    f.write(system_prompt)"]}, {"cell_type": "code", "execution_count": 56, "id": "d6beb0da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a helpful assistant tasked with answering questions using a set of tools.\n", "If the tool is not available, you can try to find the information online. You can also use your own knowledge to answer the question. \n", "You need to provide a step-by-step explanation of how you arrived at the answer.\n", "==========================\n", "Here is a few examples showing you how to answer the question step by step.\n", "\n", "Question 1: In terms of geographical distance between capital cities, which 2 countries are the furthest from each other within the ASEAN bloc according to wikipedia? Answer using a comma separated list, ordering the countries by alphabetical order.\n", "Steps:\n", "1. Search the web for \"ASEAN bloc\".\n", "2. Click the Wikipedia result for the ASEAN Free Trade Area.\n", "3. <PERSON>roll down to find the list of member states.\n", "4. Click into the Wikipedia pages for each member state, and note its capital.\n", "5. Search the web for the distance between the first two capitals. The results give travel distance, not geographic distance, which might affect the answer.\n", "6. Thinking it might be faster to judge the distance by looking at a map, search the web for \"ASEAN bloc\" and click into the images tab.\n", "7. View a map of the member countries. Since they're clustered together in an arrangement that's not very linear, it's difficult to judge distances by eye.\n", "8. Return to the Wikipedia page for each country. Click the GPS coordinates for each capital to get the coordinates in decimal notation.\n", "9. Place all these coordinates into a spreadsheet.\n", "10. Write formulas to calculate the distance between each capital.\n", "11. Write formula to get the largest distance value in the spreadsheet.\n", "12. Note which two capitals that value corresponds to: Jakarta and Naypyidaw.\n", "13. Return to the Wikipedia pages to see which countries those respective capitals belong to: Indonesia, Myanmar.\n", "Tools:\n", "1. Search engine\n", "2. Web browser\n", "3. Microsoft Excel / Google Sheets\n", "Final Answer: Indonesia, Myanmar\n", "\n", "Question 2: Review the chess position provided in the image. It is black's turn. Provide the correct next move for black which guarantees a win. Please provide your response in algebraic notation.\n", "Steps:\n", "Step 1: Evaluate the position of the pieces in the chess position\n", "Step 2: Report the best move available for black: \"Rd5\"\n", "Tools:\n", "1. Image recognition tools\n", "Final Answer: Rd5\n", "\n", "==========================\n", "Now, please answer the following question step by step.\n", "\n"]}], "source": ["# load the system prompt from the file\n", "with open('system_prompt.txt', 'r') as f:\n", "    system_prompt = f.read()\n", "print(system_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "42fde0f8", "metadata": {}, "outputs": [], "source": ["import dotenv\n", "from langgraph.graph import MessagesState, START, StateGraph\n", "from langgraph.prebuilt import tools_condition\n", "from langgraph.prebuilt import ToolNode\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langchain_community.document_loaders import WikipediaLoader\n", "from langchain_community.document_loaders import ArxivLoader\n", "from langchain_community.vectorstores import SupabaseVectorStore\n", "from langchain.tools.retriever import create_retriever_tool\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.tools import tool\n", "from supabase.client import Client, create_client\n", "\n", "# Define the retriever from supabase\n", "load_dotenv()\n", "embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-mpnet-base-v2\") #  dim=768\n", "\n", "supabase_url = os.environ.get(\"SUPABASE_URL\")\n", "supabase_key = os.environ.get(\"SUPABASE_SERVICE_KEY\")\n", "supabase: Client = create_client(supabase_url, supabase_key)\n", "vector_store = SupabaseVectorStore(\n", "    client=supabase,\n", "    embedding= embeddings,\n", "    table_name=\"documents\",\n", "    query_name=\"match_documents_langchain\",\n", ")\n", "\n", "question_retrieve_tool = create_retriever_tool(\n", "    vector_store.as_retriever(),\n", "    \"Question Retriever\",\n", "    \"Find similar questions in the vector database for the given question.\",\n", ")\n", "\n", "@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply two numbers.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a * b\n", "\n", "@tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Add two numbers.\n", "    \n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a + b\n", "\n", "@tool\n", "def subtract(a: int, b: int) -> int:\n", "    \"\"\"Subtract two numbers.\n", "    \n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a - b\n", "\n", "@tool\n", "def divide(a: int, b: int) -> int:\n", "    \"\"\"Divide two numbers.\n", "    \n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    if b == 0:\n", "        raise ValueError(\"Cannot divide by zero.\")\n", "    return a / b\n", "\n", "@tool\n", "def modulus(a: int, b: int) -> int:\n", "    \"\"\"Get the modulus of two numbers.\n", "    \n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a % b\n", "\n", "@tool\n", "def wiki_search(query: str) -> str:\n", "    \"\"\"Search Wikipedia for a query and return maximum 2 results.\n", "    \n", "    Args:\n", "        query: The search query.\"\"\"\n", "    search_docs = WikipediaLoader(query=query, load_max_docs=2).load()\n", "    formatted_search_docs = \"\\n\\n---\\n\\n\".join(\n", "        [\n", "            f'<Document source=\"{doc.metadata[\"source\"]}\" page=\"{doc.metadata.get(\"page\", \"\")}\"/>\\n{doc.page_content}\\n</Document>'\n", "            for doc in search_docs\n", "        ])\n", "    return {\"wiki_results\": formatted_search_docs}\n", "\n", "@tool\n", "def web_search(query: str) -> str:\n", "    \"\"\"Search Tavily for a query and return maximum 3 results.\n", "    \n", "    Args:\n", "        query: The search query.\"\"\"\n", "    search_docs = TavilySearchResults(max_results=3).invoke(query=query)\n", "    formatted_search_docs = \"\\n\\n---\\n\\n\".join(\n", "        [\n", "            f'<Document source=\"{doc.metadata[\"source\"]}\" page=\"{doc.metadata.get(\"page\", \"\")}\"/>\\n{doc.page_content}\\n</Document>'\n", "            for doc in search_docs\n", "        ])\n", "    return {\"web_results\": formatted_search_docs}\n", "\n", "@tool\n", "def arvix_search(query: str) -> str:\n", "    \"\"\"Search Arxiv for a query and return maximum 3 result.\n", "    \n", "    Args:\n", "        query: The search query.\"\"\"\n", "    search_docs = ArxivLoader(query=query, load_max_docs=3).load()\n", "    formatted_search_docs = \"\\n\\n---\\n\\n\".join(\n", "        [\n", "            f'<Document source=\"{doc.metadata[\"source\"]}\" page=\"{doc.metadata.get(\"page\", \"\")}\"/>\\n{doc.page_content[:1000]}\\n</Document>'\n", "            for doc in search_docs\n", "        ])\n", "    return {\"arvix_results\": formatted_search_docs}\n", "\n", "@tool\n", "def similar_question_search(question: str) -> str:\n", "    \"\"\"Search the vector database for similar questions and return the first results.\n", "    \n", "    Args:\n", "        question: the question human provided.\"\"\"\n", "    matched_docs = vector_store.similarity_search(query, 3)\n", "    formatted_search_docs = \"\\n\\n---\\n\\n\".join(\n", "        [\n", "            f'<Document source=\"{doc.metadata[\"source\"]}\" page=\"{doc.metadata.get(\"page\", \"\")}\"/>\\n{doc.page_content[:1000]}\\n</Document>'\n", "            for doc in matched_docs\n", "        ])\n", "    return {\"similar_questions\": formatted_search_docs}\n", "\n", "tools = [\n", "    multiply,\n", "    add,\n", "    subtract,\n", "    divide,\n", "    modulus,\n", "    wiki_search,\n", "    web_search,\n", "    arvix_search,\n", "    question_retrieve_tool\n", "]\n", "\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-2.0-flash\")\n", "llm_with_tools = llm.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": null, "id": "7dd0716c", "metadata": {}, "outputs": [], "source": ["# load the system prompt from the file\n", "with open('system_prompt.txt', 'r') as f:\n", "    system_prompt = f.read()\n", "\n", "\n", "# System message\n", "sys_msg = SystemMessage(content=system_prompt)\n", "\n", "# Node\n", "def assistant(state: MessagesState):\n", "    \"\"\"Assistant node\"\"\"\n", "    return {\"messages\": [llm_with_tools.invoke([sys_msg] + state[\"messages\"])]}\n", "\n", "# Build graph\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(\"assistant\", assistant)\n", "builder.add_node(\"tools\", ToolNode(tools))\n", "builder.add_edge(START, \"assistant\")\n", "builder.add_conditional_edges(\n", "    \"assistant\",\n", "    # If the latest message (result) from assistant is a tool call -> tools_condition routes to tools\n", "    # If the latest message (result) from assistant is a not a tool call -> tools_condition routes to END\n", "    tools_condition,\n", ")\n", "builder.add_edge(\"tools\", \"assistant\")\n", "\n", "# Compile graph\n", "graph = builder.compile()\n"]}, {"cell_type": "code", "execution_count": 49, "id": "f4e77216", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": null, "id": "5987d58c", "metadata": {}, "outputs": [], "source": ["question = \"\"\n", "messages = [HumanMessage(content=question)]\n", "messages = graph.invoke({\"messages\": messages})"]}, {"cell_type": "code", "execution_count": null, "id": "330cbf17", "metadata": {}, "outputs": [], "source": ["for m in messages['messages']:\n", "    m.pretty_print()"]}], "metadata": {"kernelspec": {"display_name": "aiagent", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}