# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
ENV/
env/
.env
.venv
env.bak/
venv.bak/
.python-version

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
pytest-*.xml

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Logs
*.log
logs/
log/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.project
.pydevproject
.settings/
.vs/
*.sublime-project
*.sublime-workspace

# Database
*.db
*.rdb
*.sqlite
*.sqlite3

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# macOS specific
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# AI/model files
*.h5
*.pb
*.onnx
*.tflite
*.pt
*.pth
*.weights

# Temporary files
tmp/
temp/
.tmp
*.tmp

